<div class="careers-container">
  <!-- Hero Section -->
  <section class="careers-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>
    
    <div class="hero-content">
      <div class="hero-badge">
        <span class="badge-icon">💼</span>
        <span>Join Our Team</span>
      </div>
      
      <h1 class="hero-title">
        <span class="title-word">Career</span>
        <span class="title-word highlight">Opportunities</span>
      </h1>
      
      <p class="hero-description">
        Discover exciting career opportunities and join our innovative team of professionals
      </p>
      
      <div class="hero-stats">  
        <div class="stat-item">
          <div class="stat-number">{{jobs.length}}</div>
          <div class="stat-label">Open Positions</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">50+</div>
          <div class="stat-label">Team Members</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">5+</div>
          <div class="stat-label">Departments</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <section class="careers-content">
    <div class="content-container">
      
      <!-- Filters Section -->
      <div class="filters-section">
        <div class="filters-header">
          <h2 class="filters-title">Find Your Perfect Role</h2>
          <button class="clear-filters-btn" (click)="clearFilters()">
            <span class="btn-icon">🔄</span>
            Clear Filters
          </button>
        </div>
        
        <div class="filters-grid">
          <!-- Search Bar -->
          <div class="filter-group search-group">
            <label class="filter-label">
              <span class="label-icon">🔍</span>
              Search Jobs
            </label>
            <input
              type="text"
              class="search-input"
              [(ngModel)]="searchTerm"
              (input)="onFilterChange()"
              placeholder="Search by title, company, or keywords..."
            />
          </div>

          <!-- Location Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <span class="label-icon">📍</span>
              Location
            </label>
            <select class="filter-select" [(ngModel)]="selectedLocation" (change)="onFilterChange()">
              <option *ngFor="let location of locationOptions" [value]="location">
                {{location}}
              </option>
            </select>
          </div>

          <!-- Job Type Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <span class="label-icon">⏰</span>
              Job Type
            </label>
            <select class="filter-select" [(ngModel)]="selectedType" (change)="onFilterChange()">
              <option *ngFor="let type of typeOptions" [value]="type">
                {{type}}
              </option>
            </select>
          </div>

          <!-- Department Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <span class="label-icon">🏢</span>
              Department
            </label>
            <select class="filter-select" [(ngModel)]="selectedDepartment" (change)="onFilterChange()">
              <option *ngFor="let dept of departmentOptions" [value]="dept">
                {{dept}}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Jobs Grid -->
      <div class="jobs-section">
        <div class="jobs-header">
          <h3 class="jobs-count">
            {{jobs.length}} Position{{jobs.length !== 1 ? 's' : ''}} Available
          </h3>
          <div class="pagination-info" *ngIf="!isLoading && totalPages > 1">
            Page {{currentPage}} of {{totalPages}}
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading job opportunities...</p>
        </div>

        <div class="jobs-grid" *ngIf="!isLoading">
          <div class="job-card"
               *ngFor="let job of jobs"
               [class.featured]="job.featured"
               [class.expiring-soon]="isJobExpiringSoon(job.endDate)">
            
            <!-- Featured Badge -->
            <div class="featured-badge" *ngIf="job.featured">
              <span class="badge-icon">⭐</span>
              <span>Featured</span>
            </div>

            <!-- Expiring Soon Badge -->
            <div class="expiring-badge" *ngIf="isJobExpiringSoon(job.endDate)">
              <span class="badge-icon">⏰</span>
              <span>{{getDaysRemaining(job.endDate)}} days left</span>
            </div>

            <div class="job-header">
              <div class="company-info">
                <!-- <div class="company-logo">
                  <img [src]="job.logo" [alt]="job.company + ' logo'" />
                </div> -->
                <div class="company-details">
                  <h3 class="job-title">{{job.jobTitle}}</h3>
                  <p class="company-name">{{job.company}}</p>
                </div>
              </div>
              <div class="job-type-badge">
                <span class="type-icon">{{getJobTypeIcon(job.jobType)}}</span>
                <span>{{job.jobType}}</span>
              </div>
            </div>

            <div class="job-details">
              <div class="detail-item">
                <span class="detail-icon">📍</span>
                <span>{{job.location}}</span>
              </div>
              <div class="detail-item">
                <span class="detail-icon">💰</span>
                <span>{{job.salaryPackage}}</span>
              </div>
              <!-- <div class="detail-item">
                <span class="detail-icon">👥</span>
                <span>{{job.applications}} applications</span>
              </div> -->
            </div>

            <div class="job-description">
              <p>{{job.description}}</p>
            </div>

            <div class="job-requirements">
              <h4 class="requirements-title">Key Skills:</h4>
              <div class="requirements-tags">
                <span class="requirement-tag" *ngFor="let req of job.requirements">
                  {{req}}
                </span>
              </div>
            </div>

            <div class="job-footer">
              <div class="job-dates">
                <small class="posted-date">Posted: {{job.posted | date:'MMM d, y'}}</small>
                <small class="end-date">Deadline: {{job.endDate | date:'MMM d, y'}}</small>
              </div>
              <button class="apply-btn" (click)="openApplyDialog(job)">
                <span class="btn-icon">📝</span>
                <span>Apply Now</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Pagination Controls -->
        <div class="pagination-container" *ngIf="!isLoading && totalPages > 1">
          <nav class="pagination-nav">
            <button
              class="pagination-btn prev-btn"
              [disabled]="currentPage === 1"
              (click)="prevPage()">
              <span>← Previous</span>
            </button>

            <div class="pagination-numbers">
              <button
                *ngFor="let page of getPaginationArray()"
                class="pagination-btn page-btn"
                [class.active]="page === currentPage"
                (click)="goToPage(page)">
                {{page}}
              </button>
            </div>

            <button
              class="pagination-btn next-btn"
              [disabled]="currentPage === totalPages"
              (click)="nextPage()">
              <span>Next →</span>
            </button>
          </nav>
        </div>

        <!-- No Jobs Found -->
        <div class="no-jobs" *ngIf="!isLoading && jobs.length === 0">
          <div class="no-jobs-icon">🔍</div>
          <h3>No positions found</h3>
          <p>Try adjusting your filters or search terms</p>
          <button class="clear-filters-btn" (click)="clearFilters()">
            Clear All Filters
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Application Dialog -->
  <div class="dialog-overlay" *ngIf="displayDialog" (click)="closeDialog()">
    <div class="application-dialog" (click)="$event.stopPropagation()">
      <div class="dialog-header">
        <div class="dialog-title">
          <span class="dialog-icon">📝</span>
          <div>
            <h2>Apply for {{selectedJob?.title}}</h2>
            <p>{{selectedJob?.company}}</p>
          </div>
        </div>
        <button class="close-btn" (click)="closeDialog()">
          <span>✕</span>
        </button>
      </div>

      <form class="application-form" (ngSubmit)="submitApplication()">
        <div class="form-section">
          <h3 class="section-title">Personal Information</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="firstName">First Name *</label>
              <input 
                type="text" 
                id="firstName" 
                [(ngModel)]="application.firstName" 
                name="firstName" 
                required 
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label for="lastName">Last Name *</label>
              <input 
                type="text" 
                id="lastName" 
                [(ngModel)]="application.lastName" 
                name="lastName" 
                required 
                class="form-input"
              />
            </div>
            <div class="form-group full-width">
              <label for="email">Email Address *</label>
              <input 
                type="email" 
                id="email" 
                [(ngModel)]="application.email" 
                name="email" 
                required 
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input 
                type="tel" 
                id="phone" 
                [(ngModel)]="application.phone" 
                name="phone" 
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label for="address">Address</label>
              <input 
                type="text" 
                id="address" 
                [(ngModel)]="application.address" 
                name="address" 
                class="form-input"
              />
            </div>
          </div>
        </div>
        <div class="file-drop-zone"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onFileDrop($event, 'resume')"
          [class.drag-active]="isDragActive"
          (click)="fileInput.click()">
          <div class="drop-content">
            <span class="upload-icon">📄</span>
            <p *ngIf="!resumeFile">Drag & drop your resume here or click to upload</p>
            <p *ngIf="resumeFile">Selected: {{resumeFile.name}}</p>
            <input #fileInput type="file" accept=".pdf,.doc,.docx" (change)="onFileChange($event, 'resume')" class="file-input" hidden />
          </div>
        </div>
        <small class="file-help">PDF, DOC, or DOCX format</small>
        <div class="form-section">
          <h3 class="section-title">Cover Letter</h3>
          <div class="form-group">
            <textarea 
              id="coverLetter" 
              [(ngModel)]="application.coverLetter" 
              name="coverLetter" 
              rows="4" 
              placeholder="Tell us why you're interested in this position..."
              class="form-textarea"
            ></textarea>
          </div>
        </div>



        <div class="dialog-footer">
          <button type="button" class="cancel-btn" (click)="closeDialog()">
            Cancel
          </button>
          <button type="submit" class="submit-btn" [disabled]="isSubmitting">
            <span class="btn-icon" *ngIf="!isSubmitting">✅</span>
            <span class="loading-spinner" *ngIf="isSubmitting"></span>
            <span>{{isSubmitting ? 'Submitting...' : 'Submit Application'}}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
