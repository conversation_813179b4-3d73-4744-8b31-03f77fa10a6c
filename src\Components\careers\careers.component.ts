import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { FileUploadModule } from 'primeng/fileupload';
import { JobService } from '../../services/job.service';
import { AppliedJobService } from '../../services/applied-job.service';
import { Job } from '../../models/job.model';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-careers',
  standalone: true,
  imports: [CommonModule, CardModule, DialogModule, FormsModule,FileUploadModule],
  templateUrl: './careers.component.html',
  styleUrls: ['./careers.component.css']
})
export class CareersComponent implements OnInit {
  jobs: Job[] = [];
  allJobs: Job[] = []; // Store all jobs for filtering

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 2;
  totalPages: number = 0;

  // Loading states
  isLoading: boolean = false;
  isSubmitting: boolean = false;

  selectedJob: Job | null = null;
  displayDialog: boolean = false;
  application = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    coverLetter: "",
    resume: undefined as File | undefined
  };
  files: { [key: string]: File } = {};
  appService: any;

  // Filters
  selectedLocation: string = "All Locations";
  selectedType: string = "All Types";
  selectedDepartment: string = "All Departments";
  searchTerm: string = "";

  locationOptions: string[] = ["All Locations", "Remote", "San Francisco, CA", "New York, NY"];
  typeOptions: string[] = ["All Types", "Full-time", "Part-time", "Contract"];
  departmentOptions: string[] = ["All Departments", "Engineering", "Design", "Marketing", "AI/ML"];

   constructor(
    private jobService: JobService,
    private appliedJob: AppliedJobService,
    private toastr: ToastrService
   ) {}
  

  ngOnInit(): void {
    this.loadJobs();
  }

  isDragActive: boolean = false;
  resumeFile: File | null = null;

onDragOver(event: DragEvent): void {
  event.preventDefault();
  this.isDragActive = true;
}

onDragLeave(event: DragEvent): void {
  event.preventDefault();
  this.isDragActive = false;
}

onFileDrop(event: DragEvent, type: string): void {
  event.preventDefault();
  this.isDragActive = false;

  if (event.dataTransfer?.files.length) {
    const file = event.dataTransfer.files[0];
    this.setFile(file, type);
  }
}

onFileChange(event: Event, type: string): void {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    this.setFile(file, type);
  }
}

setFile(file: File, type: string): void {
  debugger;
  if (type === 'resume') {
    this.resumeFile = file;
    this.application.resume = file;
  }
}

loadJobs(): void {
  console.log('loadJobs called - Page:', this.currentPage, 'Size:', this.itemsPerPage);
  this.isLoading = true;

  this.jobService.getJobs(this.currentPage, this.itemsPerPage).subscribe({
    next: (response: any) => {
      console.log('API Response:', response);

      if (response.items && Array.isArray(response.items)) {
        this.allJobs = response.items;
        this.updatePaginatedJobs();
      } else {
        this.allJobs = [];
        this.jobs = [];
      }

      this.totalPages = response.totalPages || 1;
      this.isLoading = false;

      console.log('Jobs loaded:', this.jobs);
      console.log('Total pages:', this.totalPages);
    },
    error: (err) => {
      console.error('Error loading jobs', err);
      this.toastr.error('Failed to load jobs. Please try again.', 'Error');
      this.isLoading = false;
    }
  });
}


  updatePaginatedJobs(): void {
    const filteredJobs = this.getFilteredJobs();
    this.totalPages = Math.ceil(filteredJobs.length / this.itemsPerPage);

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.jobs = filteredJobs.slice(startIndex, endIndex);
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadJobs();
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadJobs();
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadJobs();
    }
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  openApplyDialog(job: Job) {
    this.selectedJob = job
    this.displayDialog = true
    this.application = {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      coverLetter: "",
      resume: undefined
    }
  }

  closeDialog(): void {
    this.displayDialog = false;
    this.selectedJob = null;
    this.resetApplication();
    this.isSubmitting = false;
  }

//   onFileChange(event: any, type: string) {
//   const file = event.target.files?.[0]
//   if (file) {
//     this.files[type] = file

//     console.log("File",file, this.files)
//   } else {
//     delete this.files[type]
//   }
// }


submitApplication(): void {
  if (!this.application.firstName || !this.application.lastName || !this.application.email) {
    this.toastr.error("Please fill in all required fields.", "Validation Error");
    return;
  }

  if (!this.resumeFile) {
    this.toastr.error("Please upload your resume.", "Validation Error");
    return;
  }

  if (!this.selectedJob) {
    this.toastr.error("No job selected.", "Error");
    return;
  }

  this.isSubmitting = true;
  this.toastr.info("Submitting your application...", "Please Wait");

  const formData = new FormData();
  formData.append("JobId", this.selectedJob.jobId?.toString() || '');
  formData.append("Email", this.application.email);
  formData.append("PhoneNumber", this.application.phone || '');
  formData.append("CoverLetterPath", this.application.coverLetter || '');
  formData.append("EmployerName", this.application.firstName + ' ' + this.application.lastName || '');
  formData.append("ResumeFile", this.resumeFile);

  this.appliedJob.createAppliedJob(formData).subscribe({
    next: () => {
      this.toastr.success('Application submitted successfully!', 'Success');
      this.isSubmitting = false;
      this.closeDialog();
      this.resetApplication();
    },
    error: (err) => {
      console.error('Application submission failed', err.message);
      const errorMsg = err?.error?.message || err.message || 'Failed to submit application';
      this.toastr.error(errorMsg, 'Submission Failed');
      this.isSubmitting = false;
    }
  });
}

  resetApplication(): void {
    this.application = {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      coverLetter: "",
      resume: undefined
    };
    this.resumeFile = null;
  }

  // Filter methods (optional - for future enhancement)
  getFilteredJobs(): Job[] {
    return this.allJobs.filter((job) => {
      const locationMatch = this.selectedLocation === "All Locations" || job.location === this.selectedLocation;
      const typeMatch = this.selectedType === "All Types" || (job.type || job.jobType) === this.selectedType;
      const departmentMatch =
        this.selectedDepartment === "All Departments" || job.department === this.selectedDepartment;
      const searchMatch =
        !this.searchTerm ||
        (job.title || job.jobTitle)?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        job.company.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(this.searchTerm.toLowerCase());

      return locationMatch && typeMatch && departmentMatch && searchMatch;
    });
  }

  // Utility methods
  getDaysRemaining(endDate: Date | string | undefined): number {
    if (!endDate) return 0;
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  isJobExpiringSoon(endDate: Date | string | undefined): boolean {
    if (!endDate) return false;
    return this.getDaysRemaining(endDate) <= 7;
  }

  getJobTypeIcon(type: string): string {
    switch (type) {
      case "Full-time":
        return "💼";
      case "Part-time":
        return "⏰";
      case "Contract":
        return "📋";
      default:
        return "💼";
    }
  }

  clearFilters(): void {
    this.selectedLocation = "All Locations";
    this.selectedType = "All Types";
    this.selectedDepartment = "All Departments";
    this.searchTerm = "";
    this.currentPage = 1;
    this.loadJobs();
  }

  // Method to trigger pagination update when filters change
  onFilterChange(): void {
    this.currentPage = 1;
    this.loadJobs();
  }
}
